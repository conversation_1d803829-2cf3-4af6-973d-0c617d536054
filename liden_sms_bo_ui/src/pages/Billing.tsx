import { useState, useEffect, useCallback } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { DataTable } from "@/components/DataTable";
import { DatePickerWithRange } from "@/components/ui/date-range-picker";
import { useDateRangePickerWithQuickSelect } from "@/hooks/useDateRangePicker";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { LoadingSpinner } from "@/components/LoadingSpinner";
import { LidenAPI } from "@/lib/api-index";
import { AccountTransaction, AccountTransactionParams } from "@/lib/api-types";
import {
  CreditCard,
  Download,
  DollarSign,
  TrendingUp,
  Calendar,
  Receipt,
  Filter,
  Eye,
  FileText,
  Plus,
  Wallet,
  ArrowUpCircle,
  ArrowDownCircle,
  RefreshCw
} from "lucide-react";

const Billing = () => {
  const location = useLocation();
  const navigate = useNavigate();

  // Date picker hook with automatic API call triggering
  const datePicker = useDateRangePickerWithQuickSelect({
    onDateRangeChange: () => {
      // Refresh data when date range changes
      loadAllTransactions();
    }
  });

  // State for transactions
  const [expenditures, setExpenditures] = useState<AccountTransaction[]>([]);
  const [topUps, setTopUps] = useState<AccountTransaction[]>([]);
  const [refunds, setRefunds] = useState<AccountTransaction[]>([]);
  const [loading, setLoading] = useState(false);

  // State for metadata
  const [transactionMetadata, setTransactionMetadata] = useState({
    expenditures: { limit: 50, totalCount: 0 },
    topUps: { limit: 50, totalCount: 0 },
    refunds: { limit: 50, totalCount: 0 }
  });

  // Determine active tab from URL
  const getActiveTabFromPath = () => {
    const path = location.pathname;
    if (path.includes('/payment-methods')) return 'payment-methods';
    if (path.includes('/top-ups')) return 'top-ups';
    if (path.includes('/expenditures')) return 'expenditures';
    if (path.includes('/refunds')) return 'refunds';
    return 'payment-methods'; // default
  };

  const [activeTab, setActiveTab] = useState(getActiveTabFromPath());

  // Handle tab change and update URL
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    const basePath = '/billing';
    const newPath = value === 'payment-methods' ? basePath : `${basePath}/${value}`;
    navigate(newPath);
  };

  // Update active tab when URL changes
  useEffect(() => {
    setActiveTab(getActiveTabFromPath());
  }, [location.pathname]);

  // Top-up form state
  const [topUpAmount, setTopUpAmount] = useState("");
  const [topUpMethod, setTopUpMethod] = useState("");

  // Fetch transactions based on reference type
  const fetchTransactions = useCallback(async (referenceType: string) => {
    try {
      setLoading(true);
      const dateParams = datePicker.getAPIDateParams();
      const params: AccountTransactionParams = {
        start: dateParams.start || '2025-06-01',
        end: dateParams.end || '2025-09-23',
        referenceType: referenceType,
        limit: 50
      };

      const response = await LidenAPI.accountTransactions.getTransactions(params);

      if (response.status === 200 && response.data) {
        // Extract limit from message field (e.g., "Query returned 50result(s)" → 50)
        const message = response.message || '';
        const limitMatch = message.match(/Query returned (\d+)result/);
        const extractedLimit = limitMatch ? parseInt(limitMatch[1]) : 50;

        // Return the data array with metadata
        return {
          data: response.data || [],
          limit: extractedLimit,
          totalCount: response.data?.[0]?.total_count ? parseInt(response.data[0].total_count) : 0
        };
      }
      return { data: [], limit: 50, totalCount: 0 };
    } catch (error) {
      console.error(`Failed to fetch transactions for type ${referenceType}:`, error);
      return [];
    } finally {
      setLoading(false);
    }
  }, [datePicker]);

  // Load all transaction types
  const loadAllTransactions = useCallback(async () => {
    setLoading(true);
    try {
      const [expendituresData, topUpsData, refundsData] = await Promise.all([
        fetchTransactions('1'), // Expenditures/Debits
        fetchTransactions('2'), // Top-ups/Credits
        fetchTransactions('3'), // Refunds
      ]);

      // Extract data and metadata - handle both old and new response formats
      const extractData = (response: any) => {
        if (Array.isArray(response)) {
          return { data: response, limit: 50, totalCount: 0 };
        }
        return response;
      };

      const expendituresResult = extractData(expendituresData);
      const topUpsResult = extractData(topUpsData);
      const refundsResult = extractData(refundsData);

      setExpenditures(expendituresResult.data || []);
      setTopUps(topUpsResult.data || []);
      setRefunds(refundsResult.data || []);

      // Update metadata
      setTransactionMetadata({
        expenditures: { limit: expendituresResult.limit, totalCount: expendituresResult.totalCount },
        topUps: { limit: topUpsResult.limit, totalCount: topUpsResult.totalCount },
        refunds: { limit: refundsResult.limit, totalCount: refundsResult.totalCount }
      });
    } catch (error) {
      console.error('Failed to load transactions:', error);
    } finally {
      setLoading(false);
    }
  }, [fetchTransactions]);

  useEffect(() => {
    loadAllTransactions();
  }, [loadAllTransactions]);

  // Handle top-up form submission
  const handleTopUpSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Implement top-up functionality
    console.log('Top-up submitted:', { amount: topUpAmount, method: topUpMethod });
  };

  // Transaction table columns
  const transactionColumns = [
    {
      key: "transacion_id",
      label: "Transaction ID",
      accessorKey: "transacion_id",
      header: "Transaction ID",
      render: (value: string) => (
        <span className="font-mono text-sm">{value}</span>
      ),
    },
    {
      key: "amount",
      label: "Amount",
      accessorKey: "amount",
      header: "Amount",
      render: (value: string, row: any) => {
        const amount = parseFloat(value);
        const isNegative = amount < 0;
        return (
          <span className={`font-medium ${isNegative ? 'text-red-600' : 'text-green-600'}`}>
            {row.currency} {Math.abs(amount).toFixed(2)}
          </span>
        );
      },
    },
    {
      key: "reference_type",
      label: "Type",
      accessorKey: "reference_type",
      header: "Type",
      render: (value: string) => (
        <Badge variant={value === "DEBIT" ? "destructive" : "default"}>
          {value}
        </Badge>
      ),
    },
    {
      key: "description",
      label: "Description",
      accessorKey: "description",
      header: "Description",
      render: (value: string) => (
        <span className="text-sm max-w-xs truncate">{value}</span>
      ),
    },
    {
      key: "source",
      label: "Source",
      accessorKey: "source",
      header: "Source",
      render: (value: string) => (
        <span className="text-sm text-muted-foreground">{value}</span>
      ),
    },
    {
      key: "created_at",
      label: "Date",
      accessorKey: "created_at",
      header: "Date",
      render: (value: string) => (
        <span className="text-sm">{new Date(value).toLocaleDateString()}</span>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <Wallet className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground">Billing & Account Management</h1>
            <p className="text-muted-foreground">Manage your account balance, transactions, and payment methods</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={loadAllTransactions} disabled={loading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button>
            <Download className="mr-2 h-4 w-4" />
            Export Data
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <ArrowDownCircle className="h-5 w-5 text-red-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Expenditures</p>
                <p className="text-2xl font-bold">
                  {expenditures.reduce((sum, transaction) => sum + Math.abs(parseFloat(transaction.amount || '0')), 0).toLocaleString('en-US', {
                    style: 'currency',
                    currency: expenditures[0]?.currency || 'KES'
                  })}
                </p>
                <p className="text-xs text-muted-foreground">{expenditures.length} transactions</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <ArrowUpCircle className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Top-ups</p>
                <p className="text-2xl font-bold">
                  {topUps.reduce((sum, transaction) => sum + Math.abs(parseFloat(transaction.amount || '0')), 0).toLocaleString('en-US', {
                    style: 'currency',
                    currency: topUps[0]?.currency || 'KES'
                  })}
                </p>
                <p className="text-xs text-muted-foreground">{topUps.length} transactions</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <RefreshCw className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Refunds</p>
                <p className="text-2xl font-bold">
                  {refunds.reduce((sum, transaction) => sum + Math.abs(parseFloat(transaction.amount || '0')), 0).toLocaleString('en-US', {
                    style: 'currency',
                    currency: refunds[0]?.currency || 'KES'
                  })}
                </p>
                <p className="text-xs text-muted-foreground">{refunds.length} transactions</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-orange-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Date Range</p>
                <p className="text-sm font-bold">
                  {datePicker.formattedRange || "Select date range"}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-4">
        <TabsList>
          <TabsTrigger value="payment-methods">Payment Methods</TabsTrigger>
          <TabsTrigger value="top-ups">Top Ups</TabsTrigger>
          <TabsTrigger value="expenditures">Expenditures</TabsTrigger>
          <TabsTrigger value="refunds">Refunds</TabsTrigger>
        </TabsList>

        {/* Payment Methods Tab */}
        <TabsContent value="payment-methods" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Account Top Up</CardTitle>
                <CardDescription>Add funds to your account balance</CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleTopUpSubmit} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="amount">Amount (KES)</Label>
                    <Input
                      id="amount"
                      type="number"
                      placeholder="Enter amount"
                      value={topUpAmount}
                      onChange={(e) => setTopUpAmount(e.target.value)}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="method">Payment Method</Label>
                    <Select value={topUpMethod} onValueChange={setTopUpMethod}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select payment method" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="mpesa">M-Pesa</SelectItem>
                        <SelectItem value="card">Credit/Debit Card</SelectItem>
                        <SelectItem value="bank">Bank Transfer</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <Button type="submit" className="w-full">
                    <Plus className="mr-2 h-4 w-4" />
                    Top Up Now
                  </Button>
                </form>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Payment Methods</CardTitle>
                <CardDescription>Manage your payment methods</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <CreditCard className="h-8 w-8 text-blue-500" />
                        <div>
                          <p className="font-medium">M-Pesa</p>
                          <p className="text-sm text-muted-foreground">Mobile Money</p>
                        </div>
                      </div>
                      <Badge variant="outline">Primary</Badge>
                    </div>
                  </div>
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <CreditCard className="h-8 w-8 text-green-500" />
                        <div>
                          <p className="font-medium">Bank Transfer</p>
                          <p className="text-sm text-muted-foreground">Direct bank payment</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Top Ups Tab */}
        <TabsContent value="top-ups" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Top-up History</CardTitle>
                  <CardDescription>View all account top-up transactions</CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <DatePickerWithRange
                    date={datePicker.dateRange}
                    onDateChange={datePicker.setDateRange}
                    placeholder="Select date range"
                  />
                  <Button variant="outline" size="sm" onClick={loadAllTransactions}>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Refresh
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <LoadingSpinner />
                </div>
              ) : (
                <DataTable
                  columns={transactionColumns}
                  data={topUps}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Expenditures Tab */}
        <TabsContent value="expenditures" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Expenditure History</CardTitle>
                  <CardDescription>View all account debit transactions and service usage</CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <DatePickerWithRange
                    date={datePicker.dateRange}
                    onDateChange={datePicker.setDateRange}
                    placeholder="Select date range"
                  />
                  <Button variant="outline" size="sm" onClick={loadAllTransactions}>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Refresh
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <LoadingSpinner />
                </div>
              ) : (
                <DataTable
                  columns={transactionColumns}
                  data={expenditures}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Refunds Tab */}
        <TabsContent value="refunds" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Refund History</CardTitle>
                  <CardDescription>View all refund transactions and credits</CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <DatePickerWithRange
                    date={datePicker.dateRange}
                    onDateChange={datePicker.setDateRange}
                    placeholder="Select date range"
                  />
                  <Button variant="outline" size="sm" onClick={loadAllTransactions}>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Refresh
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <LoadingSpinner />
                </div>
              ) : (
                <DataTable
                  columns={transactionColumns}
                  data={refunds}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Billing;
