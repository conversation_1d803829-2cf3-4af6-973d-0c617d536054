// Central API service for handling all HTTP requests
import {
  ApiResponse,
  LoginRequest,
  LoginResponse,
  ForgotPasswordRequest,
  ResetPasswordRequest,
  UserProfile,
  SMSBlastRequest,
  SMSMessage,
  SMSAnalytics,
  PaginationParams,
  DateRangeParams,
  ExportParams,
  SurveyCreateRequest,
  SurveyResponse,
  SurveyApplication,
  Survey,
  SurveysResponse,
  SurveyParams,
  CampaignAnalyticsResponse,
  CampaignSummaryResponse,
  AirtimeSingleRequest,
  AirtimeBulkRequest,
  MpesaPayoutRequest,
  MpesaB2BPayoutRequest,
  UtilityTransaction,
  UtilityTransactionParams,
  UtilityTransactionsResponse,
  AccountTransaction,
  AccountTransactionsResponse,
  AccountTransactionParams
} from './api-types';
import { CookieManager } from './utils';
import { ErrorHandler, type ApiError } from './error-handler';

// Re-export for backward compatibility
export type { ApiResponse };

export interface ApiRequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  params?: Record<string, string | number>;
}

class ApiService {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;

  constructor(baseURL: string = 'https://app.apiproxy.co') {
    this.baseURL = baseURL;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-Requested-With': 'XMLHttpRequest',
    };

    // Initialize auth token from storage if available
    this.initializeAuthToken();
  }

  // Initialize authentication token from storage
  private initializeAuthToken() {
    const token = this.getAuthToken();
    if (token) {
      this.setAuthToken(token);
    }
  }

  // Set authentication token
  setAuthToken(token: string) {
    // console.log('Setting X-Authorization-Key to:', token);
    this.defaultHeaders['X-Authorization-Key'] = token;
  }

  // Remove authentication token
  removeAuthToken() {
    delete this.defaultHeaders['X-Authorization-Key'];
  }

  // Check if token is valid and not expired
  isTokenValid(): boolean {
    return CookieManager.isTokenValid();
  }

  // Get token expiration information
  getTokenExpirationInfo() {
    return CookieManager.getTokenExpirationInfo();
  }

  // Check if token will expire soon (within 5 minutes)
  isTokenExpiringSoon(): boolean {
    const { timeRemaining } = this.getTokenExpirationInfo();
    return timeRemaining > 0 && timeRemaining < 5 * 60 * 1000; // 5 minutes in milliseconds
  }

  // Get current auth token
  getAuthToken(): string | null {
    // Use cookie manager for secure token retrieval
    const token = CookieManager.getAuthToken();
    if (process.env.NODE_ENV === 'development') {
      // console.log('Retrieved auth token:', token ? `${token.substring(0, 50)}...` : 'null');
    }
    return token;
  }

  // Build URL with query parameters
  private buildURL(endpoint: string, params?: Record<string, string | number>): string {
    const url = new URL(endpoint, this.baseURL);
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        url.searchParams.append(key, String(value));
      });
    }
    
    return url.toString();
  }

  // Parse headers for request
  private parseHeaders(customHeaders?: Record<string, string>): Record<string, string> {
    const headers = { ...this.defaultHeaders };

    // Get the current token from cookies
    const currentToken = this.getAuthToken();
    if (currentToken) {
      // Check if token is valid before using it
      if (CookieManager.isTokenValid()) {
        headers['X-Authorization-Key'] = currentToken;
      } else {
        // Token exists but is expired, clear it once
        console.warn('Token expired, clearing authentication data');
        CookieManager.clearAuthData();
        this.removeAuthToken();
        // Don't include expired token in headers
        delete headers['X-Authorization-Key'];
      }
    }

    // Merge custom headers
    if (customHeaders) {
      Object.assign(headers, customHeaders);
    }

    return headers;
  }

  // Main request method
  async request<T = any>(endpoint: string, config: ApiRequestConfig = {}): Promise<ApiResponse<T>> {
    const {
      method = 'GET',
      headers: customHeaders,
      body,
      params
    } = config;

    try {
      const url = this.buildURL(endpoint, params);
      const headers = this.parseHeaders(customHeaders);

      // Debug: Log headers for development
      if (process.env.NODE_ENV === 'development') {
        // console.log('API Request:', { url, method, headers });
        // console.log('X-Authorization-Key being sent:', headers['X-Authorization-Key']);
      }

      const requestConfig: RequestInit = {
        method,
        headers,
      };

      // Add body for non-GET requests
      if (body && method !== 'GET') {
        if (headers['Content-Type'] === 'application/json') {
          requestConfig.body = JSON.stringify(body);
        } else {
          requestConfig.body = body;
        }
      }

      const response = await fetch(url, requestConfig);
      
      // Handle different response types
      let data: any;
      const contentType = response.headers.get('content-type');
      
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        data = await response.text();
      }

      if (!response.ok) {
        // Handle authentication errors specifically
        if (response.status === 401 || response.status === 403) {
          console.warn('Authentication failed, clearing token data');
          CookieManager.clearAuthData();
          this.removeAuthToken();
        }

        // Handle the specific "Mandatory fields required!!" error
        if (data && typeof data === 'object' && data.statusDescription === 'Mandatory fields required!!') {
          throw new Error('Authentication token missing or invalid. Please log in again.');
        }

        throw new Error(data.message || data.statusDescription || `HTTP error! status: ${response.status}`);
      }

      return {
        data,
        success: true,
        message: data.message
      };

    } catch (error) {
      console.error('API Request failed:', error);

      // Parse error using error handler
      const apiError = ErrorHandler.parseApiError(error);

      // Handle authentication errors
      if (apiError.isAuthError) {
        ErrorHandler.handleAuthError(apiError);
      }

      return {
        data: null,
        success: false,
        message: apiError.message,
        errors: [apiError.message]
      };
    }
  }

  // Convenience methods
  async get<T = any>(endpoint: string, params?: Record<string, string | number>, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET', params, headers });
  }

  async post<T = any>(endpoint: string, body?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'POST', body, headers });
  }

  async put<T = any>(endpoint: string, body?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'PUT', body, headers });
  }

  async patch<T = any>(endpoint: string, body?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'PATCH', body, headers });
  }

  async delete<T = any>(endpoint: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE', headers });
  }

  // File upload method
  async uploadFile<T = any>(endpoint: string, file: File, additionalData?: Record<string, any>): Promise<ApiResponse<T>> {
    const formData = new FormData();
    formData.append('file', file);

    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, String(value));
      });
    }

    return this.request<T>(endpoint, {
      method: 'POST',
      body: formData,
      headers: {} // Let browser set Content-Type for FormData
    });
  }

  // Enhanced request method with automatic error handling and retry
  async requestWithErrorHandling<T = any>(
    endpoint: string,
    config: ApiRequestConfig = {},
    options: {
      showErrorMessage?: boolean;
      maxRetries?: number;
      handleAuthErrors?: boolean;
    } = {}
  ): Promise<ApiResponse<T>> {
    const {
      showErrorMessage = true,
      maxRetries = 2,
      handleAuthErrors = true
    } = options;

    return ErrorHandler.handleApiCall(
      () => this.request<T>(endpoint, config),
      {
        showErrorMessage,
        retryConfig: { maxRetries, retryDelay: 1000, backoffMultiplier: 2 },
        handleAuthErrors
      }
    );
  }
}

// Create and export singleton instance
export const apiService = new ApiService();

// ============================================================================
// AUTHENTICATION API METHODS
// ============================================================================

export class AuthAPI {
  /**
   * User login
   */
  static async login(request: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    return apiService.post<LoginResponse>(endpoints.auth.login, request);
  }

  /**
   * User logout
   */
  static async logout(): Promise<ApiResponse<void>> {
    return apiService.post<void>(endpoints.auth.logout);
  }

  /**
   * Forgot password
   */
  static async forgotPassword(request: ForgotPasswordRequest): Promise<ApiResponse<void>> {
    return apiService.post<void>(endpoints.auth.forgotPassword, request);
  }

  /**
   * Reset password
   */
  static async resetPassword(request: ResetPasswordRequest): Promise<ApiResponse<void>> {
    return apiService.post<void>(endpoints.auth.resetPassword, request);
  }

  /**
   * Get user profile
   */
  static async getProfile(): Promise<ApiResponse<UserProfile>> {
    return apiService.get<UserProfile>(endpoints.auth.profile);
  }
}

// ============================================================================
// SMS API METHODS
// ============================================================================

export class SMSAPI {
  /**
   * Send bulk SMS blast
   */
  static async sendBlast(request: SMSBlastRequest): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.sms.bulk.send, request);
  }

  /**
   * Get SMS outbox
   */
  static async getOutbox(params?: PaginationParams & DateRangeParams & ExportParams): Promise<ApiResponse<SMSMessage[]>> {
    return apiService.get<SMSMessage[]>(endpoints.sms.bulk.outbox, params);
  }

  /**
   * Get SMS analytics
   */
  static async getAnalytics(params?: DateRangeParams): Promise<ApiResponse<SMSAnalytics>> {
    return apiService.get<SMSAnalytics>(endpoints.sms.bulk.analytics, params);
  }

  /**
   * Get campaign analytics
   */
  static async getCampaignAnalytics(campaignId: string): Promise<ApiResponse<CampaignAnalyticsResponse>> {
    return apiService.get<CampaignAnalyticsResponse>(endpoints.sms.bulk.campaignAnalytics, { campaignId });
  }

  /**
   * Get campaign summary
   */
  static async getCampaignSummary(campaignId: string): Promise<ApiResponse<CampaignSummaryResponse>> {
    return apiService.get<CampaignSummaryResponse>(endpoints.sms.bulk.campaignSummary.replace('{campaignId}', campaignId));
  }

  /**
   * Get SMS messages
   */
  static async getMessages(params?: PaginationParams & DateRangeParams): Promise<ApiResponse<SMSMessage[]>> {
    return apiService.get<SMSMessage[]>(endpoints.sms.bulk.messages, params);
  }

  /**
   * Get SMS blacklist
   */
  static async getBlacklist(params?: PaginationParams): Promise<ApiResponse<any[]>> {
    return apiService.get<any[]>(endpoints.sms.bulk.blacklist, params);
  }

  /**
   * Send premium SMS
   */
  static async sendPremium(request: any): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.sms.premium, request);
  }

  /**
   * Send shortcode SMS
   */
  static async sendShortcode(request: any): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.sms.shortcode, request);
  }

  /**
   * Send alphanumeric SMS
   */
  static async sendAlphanumeric(request: any): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.sms.alphanumeric, request);
  }

  /**
   * Get SMS networks
   */
  static async getNetworks(params?: any): Promise<ApiResponse<any[]>> {
    return apiService.get<any[]>(endpoints.sms.networks, params);
  }
}

// ============================================================================
// SURVEY API METHODS
// ============================================================================

export class SurveyAPI {
  /**
   * Get survey active channels
   */
  static async getActiveChannels(): Promise<ApiResponse<any[]>> {
    return apiService.get(endpoints.survey.activeChannels);
  }

  /**
   * Get survey charges
   */
  static async getCharges(): Promise<ApiResponse<any[]>> {
    return apiService.get(endpoints.survey.charges);
  }

  /**
   * Create customer survey
   */
  static async create(request: SurveyCreateRequest): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.survey.create, request);
  }

  /**
   * Add survey respondents
   */
  static async addRespondents(appId: string, request: any): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.survey.addRespondents.replace('{appId}', appId), request);
  }

  /**
   * Edit survey settings
   */
  static async edit(appId: string, request: any): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.survey.edit.replace('{appId}', appId), request);
  }

  /**
   * Stop survey
   */
  static async stop(appId: string, request: any): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.survey.stop.replace('{appId}', appId), request);
  }

  /**
   * Send SMS blast for survey
   */
  static async sendSMSBlast(settingId: string, request: any): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.survey.sendSMS.replace('{settingId}', settingId), request);
  }

  /**
   * View survey responses
   */
  static async getResponses(appId: string, params?: PaginationParams & DateRangeParams & {
    responseId?: string;
    msisdn?: string;
    questionId?: string;
  }): Promise<ApiResponse<SurveyResponse[]>> {
    return apiService.get<SurveyResponse[]>(endpoints.survey.responses.replace('{appId}', appId), params);
  }

  /**
   * View all survey applications
   */
  static async getApplications(params?: PaginationParams & DateRangeParams & {
    msisdn?: string;
    chSource?: string;
    channel?: string;
    surveyName?: string;
    clientId?: string;
  }): Promise<ApiResponse<SurveyApplication[]>> {
    return apiService.get<SurveyApplication[]>(endpoints.survey.applications, params);
  }

  /**
   * Get all surveys
   */
  static async getAllSurveys(params?: SurveyParams): Promise<ApiResponse<SurveysResponse>> {
    return apiService.get<SurveysResponse>(endpoints.survey.all, params);
  }

  /**
   * View incentive rewards transactions
   */
  static async getIncentiveTransactions(appId: string, params?: PaginationParams & DateRangeParams & {
    msisdn?: string;
    statusCode?: string;
    statusDesc?: string;
    recieptNumber?: string;
    rewardAmount?: string;
    responseId?: string;
  }): Promise<ApiResponse<any[]>> {
    return apiService.get(endpoints.survey.incentiveTransactions.replace('{appId}', appId), params);
  }

  /**
   * Get question types
   */
  static async getQuestionTypes(): Promise<ApiResponse<any[]>> {
    return apiService.get(endpoints.survey.questionTypes);
  }

  /**
   * Get incentive types
   */
  static async getIncentiveTypes(): Promise<ApiResponse<any[]>> {
    return apiService.get(endpoints.survey.incentiveTypes);
  }

  /**
   * Execute survey app
   */
  static async execute(request: any): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.survey.execute, request);
  }
}

// ============================================================================
// UTILITY/AIRTIME API METHODS
// ============================================================================

export class UtilityAPI {
  /**
   * Get utility transactions
   */
  static async getTransactions(params?: UtilityTransactionParams): Promise<ApiResponse<UtilityTransactionsResponse>> {
    return apiService.get<UtilityTransactionsResponse>(endpoints.utilities.transactions, params);
  }

  /**
   * Send single airtime
   */
  static async sendAirtime(request: AirtimeSingleRequest): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.utility.sendAirtime, request);
  }

  /**
   * Send bulk airtime
   */
  static async sendBulkAirtime(request: AirtimeBulkRequest): Promise<ApiResponse<any>> {
    return apiService.uploadFile(endpoints.utility.bulkAirtime, request.uploadedFile, {
      clientId: request.clientId,
      countryCode: request.countryCode,
      callbackUrl: request.callbackUrl,
      uniqueId: request.uniqueId
    });
  }

  /**
   * Bulk service approval
   */
  static async bulkApproval(request: any): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.utility.approval, request);
  }

  /**
   * M-pesa payout
   */
  static async mpesaPayout(request: MpesaPayoutRequest): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.utility.mpesaPayout, request);
  }

  /**
   * M-pesa B2B payout
   */
  static async mpesaB2BPayout(request: MpesaB2BPayoutRequest): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.utility.mpesaB2BPayout, request);
  }

  /**
   * Query M-pesa transaction status
   */
  static async queryMpesaStatus(id: string): Promise<ApiResponse<any>> {
    return apiService.get(`${endpoints.utility.mpesaStatus}?id=${id}`);
  }

  /**
   * Activate airtime service
   */
  static async activateAirtime(request: { status: string; clientId: string; discounts: any[] }): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.utility.activateAirtime, request);
  }

  /**
   * View utility reports
   */
  static async getUtilityReports(params?: PaginationParams & DateRangeParams & ExportParams & {
    status?: string;
    amount?: string;
    recipients?: string;
    clientId?: string;
    ipAddress?: string;
    ipApproval?: string;
  }): Promise<ApiResponse<any[]>> {
    return apiService.get(endpoints.utility.reports, params);
  }

  /**
   * View utility transactions
   */
  static async getUtilityTransactions(id: string, params?: PaginationParams & DateRangeParams & ExportParams & {
    status?: string;
    amount?: string;
    recipients?: string;
    clientId?: string;
    ipAddress?: string;
    ipApproval?: string;
    uniqueId?: string;
    accountNumber?: string;
    utilityBulkId?: string;
    statusDesc?: string;
  }): Promise<ApiResponse<UtilityTransaction[]>> {
    return apiService.get<UtilityTransaction[]>(endpoints.utility.transactions.replace('{id}', id), params);
  }
}

// ============================================================================
// ACCOUNT TRANSACTIONS API METHODS
// ============================================================================

export class AccountTransactionsAPI {
  /**
   * Get account transactions (expenditures, top-ups, refunds)
   */
  static async getTransactions(params?: AccountTransactionParams): Promise<any> {
    // return apiService.get<AccountTransactionsResponse>(endpoints.accountTransactions.view, params);
    const resp = apiService.get(endpoints.accountTransactions.view, params);
    let r = {
      status:(await resp).data.data.code,
      message:(await resp).data.data.message,
      data:(await resp).data.data.data,

    };
    return r;
  }
}

// Export specific API endpoints based on real Liden APIs
export const endpoints = {
  // Authentication
  auth: {
    login: '/account/v1/grant_access',
    logout: '/account/v1/logout',
    forgotPassword: '/account/v1/forgot-password',
    resetPassword: '/account/v1/reset-password',
    profile: '/account/v1/profile',
  },

  // SMS
  sms: {
    bulk: {
      outbox: '/sms/v1/view/outbox',
      analytics: '/sms/v1/analytics',
      messages: '/sms/v1/view/messages',
      blacklist: '/sms/v1/blacklist',
      send: '/sms/v1/blast/send_group',
      campaignAnalytics: '/account/v1/view/bulk_analytics',
      campaignSummary: '/account/v1/view/bulk_summary/{campaignId}',
    },
    premium: '/sms/v1/premium',
    shortcode: '/sms/v1/shortcode',
    alphanumeric: '/sms/v1/alphanumeric',
    networks: '/sms/v1/view/networks',
  },

  // Contact Management
  contact: {
    view: '/contact/v1/view/contacts',
    edit: '/contact/v1/edit_entry/{contactId}',
    groups: '/contact/v1/view/groups',
    updateGroup: '/contact/v1/update/group/{groupId}',
  },

  // Utilities/Airtime
  utilities: {
    transactions: '/account/v1/view/utilities',
  },

  // Analytics
  analytics: {
    dashboard: '/account/v1/view/dashboard_stats',
    campaigns: '/analytics/campaigns',
    delivery: '/analytics/delivery',
  },

  // Survey
  survey: {
    activeChannels: '/survey/v1/view/active_channels',
    charges: '/survey/v1/view/charges',
    create: '/survey/v1/create',
    addRespondents: '/survey/v1/add/respondents/{appId}',
    edit: '/survey/v1/edit/{appId}',
    stop: '/survey/v1/stop/{appId}',
    sendSMS: '/survey/v1/send_sms/{settingId}',
    responses: '/survey/v1/view/responses/{appId}',
    applications: '/survey/v1/view/all',
    all: '/survey/v1/view/all',
    incentiveTransactions: '/survey/v1/view/incentives_trxn/{appId}',
    questionTypes: '/survey/v1/view/question_types',
    incentiveTypes: '/survey/v1/view/incentives',
    execute: '/survey/v1/execute',
  },

  // USSD
  ussd: {
    safaricomGateway: '/ussd/v1/gw/ke/safaricom',
    types: '/ussd/v1/view/types',
    accessPoints: '/ussd/v1/view/access_points',
    apps: '/ussd/v1/view/apps',
    createApp: '/ussd/v1/create_app',
    configureAccessPoint: '/ussd/v1/configure/access_point',
  },

  // Utility/Airtime
  utility: {
    sendAirtime: '/bill/v1/send_airtime',
    bulkAirtime: '/bill/v1/bulk_airtime',
    approval: '/bill/v1/approval',
    mpesaPayout: '/bill/v1/mpesa_payout',
    mpesaB2BPayout: '/bill/v1/mpesa_b2b_payout',
    mpesaStatus: '/bill/v1/status/mpesa_payout',
    activateAirtime: '/activate/v1/airtime',
    reports: '/account/v1/view/utilities',
    transactions: '/account/v1/view/utility/transactions/{id}',
  },

  // Client Management
  client: {
    addUser: '/account/v1/add_users',
    editUser: '/account/v1/edit_user',
    addPermissions: '/account/v1/add/user_permissions',
    configureMpesa: '/account/v1/configure/add_paybills',
    editMpesa: '/account/v1/configure/edit_paybills',
    activateAccount: '/activate/v1/client_account',
    countries: '/account/v1/view/countries',
    wallet: '/account/v1/view/wallet',
    auditLogs: '/account/v1/view/user/audit_logs',
    invoices: '/account/v1/view/invoices',
    dashboardStats: '/account/v1/view/dashboard_stats',
    bulkUsage: '/account/v1/view/bulk_usage',
    bulkMessages: '/account/v1/view/bulk_messages',
    senderIds: '/account/v1/view/sender_ids',
  },



  // Voice
  voice: {
    types: '/voice/v1/view/types',
  },

  // Scheduler
  scheduler: {
    execute: '/scheduler/v1/execute',
  },

  // Webhooks
  webhooks: {
    atCallback: '/webhook/v1/at_callback/validate',
    c2bConfirmation: '/webhook/v1/c2b_confirmation',
  },

  // Premium Content
  subscription: {
    messageReports: '/subscription/v1/view/message_reports',
  },

  // Bulk Rate Card
  bulkRateCard: {
    view: '/account/v1/view/bulk_ratecard',
  },

  // Invoice
  invoice: {
    view: '/account/v1/view/invoices',
  },

  // User Management
  userManagement: {
    users: '/account/v1/view/users',
    roles: '/account/v1/view/user_roles',
    create: '/account/v1/add_users',
    update: '/account/v1/edit_user',
    delete: '/account/v1/delete_user/{userId}',
  },

  // Account Transactions
  accountTransactions: {
    view: '/account/v1/view/transactions',
  },
};

export default apiService;
