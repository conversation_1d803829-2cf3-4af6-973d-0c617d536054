import { useState, ReactNode } from "react"
import { Search, Download, Filter, MoreHorizontal, ChevronLeft, ChevronRight } from "lucide-react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"

export interface Column {
  key: string
  label: string
  sortable?: boolean
  className?: string
  render?: (value: any, row: any) => ReactNode
}

export interface DataTableProps {
  data: any[]
  columns: Column[]
  title?: string
  searchable?: boolean
  searchPlaceholder?: string
  filters?: ReactNode
  actions?: ReactNode
  pageSize?: number
  pageSizes?: number[]
  showExport?: boolean
  exportFormats?: string[]
  onSearch?: (query: string) => void
  onPageChange?: (page: number) => void
  onPageSizeChange?: (size: number) => void
  onExport?: (format: string) => void
  className?: string
  mobileCardRender?: (row: any, index: number) => ReactNode
  // New props for server-side pagination
  totalCount?: number
  currentPage?: number
  loading?: boolean
}

export function DataTable({
  data,
  columns,
  title,
  searchable = true,
  searchPlaceholder = "Search...",
  filters,
  actions,
  pageSize = 10,
  pageSizes = [10, 25, 50, 100],
  showExport = true,
  exportFormats = ["CSV", "Excel", "PDF"],
  onSearch,
  onPageChange,
  onPageSizeChange,
  onExport,
  className,
  mobileCardRender,
  totalCount,
  currentPage: externalCurrentPage,
  loading = false,
}: DataTableProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [internalCurrentPage, setInternalCurrentPage] = useState(1)
  const [currentPageSize, setCurrentPageSize] = useState(pageSize)

  // Use external currentPage if provided, otherwise use internal state
  const currentPage = externalCurrentPage ?? internalCurrentPage

  // Ensure data is always an array
  const safeData = Array.isArray(data) ? data : []

  // Calculate pagination based on totalCount if provided, otherwise use data.length
  const actualTotalCount = totalCount ?? safeData.length
  const totalPages = Math.ceil(actualTotalCount / currentPageSize)

  // For server-side pagination, use all data as it's already paginated
  // For client-side pagination, slice the data
  const paginatedData = totalCount ? safeData : safeData.slice((currentPage - 1) * currentPageSize, currentPage * currentPageSize)

  // Calculate display indices for pagination info
  const startIndex = (currentPage - 1) * currentPageSize
  const endIndex = startIndex + paginatedData.length

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    onSearch?.(query)
    if (!externalCurrentPage) {
      setInternalCurrentPage(1) // Reset to first page when searching
    }
  }

  const handlePageChange = (page: number) => {
    if (!externalCurrentPage) {
      setInternalCurrentPage(page)
    }
    onPageChange?.(page)
  }

  const handlePageSizeChange = (size: string) => {
    const newSize = parseInt(size)
    setCurrentPageSize(newSize)
    if (!externalCurrentPage) {
      setInternalCurrentPage(1) // Reset to first page when changing page size
    }
    onPageSizeChange?.(newSize)
  }

  const handleExport = (format: string) => {
    onExport?.(format)
  }

  // Generate pagination items
  const generatePaginationItems = () => {
    const items = []
    const maxVisible = 5
    let startPage = Math.max(1, currentPage - Math.floor(maxVisible / 2))
    let endPage = Math.min(totalPages, startPage + maxVisible - 1)

    if (endPage - startPage + 1 < maxVisible) {
      startPage = Math.max(1, endPage - maxVisible + 1)
    }

    for (let i = startPage; i <= endPage; i++) {
      items.push(
        <PaginationItem key={i}>
          <PaginationLink
            onClick={() => handlePageChange(i)}
            isActive={currentPage === i}
            className={cn(
              "rounded-full cursor-pointer",
              currentPage === i && "dark:bg-liden-coral dark:text-white dark:hover:bg-liden-coral-hover light:bg-liden-green light:text-white"
            )}
          >
            {i}
          </PaginationLink>
        </PaginationItem>
      )
    }

    return items
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          {title && <h2 className="text-xl font-semibold text-foreground">{title}</h2>}
          <p className="text-sm text-muted-foreground">
            Showing {startIndex + 1} to {Math.min(endIndex, actualTotalCount)} of {actualTotalCount} results
          </p>
        </div>
        
        <div className="flex flex-wrap items-center gap-2">
          {/* Page Size Selector */}
          <Select value={currentPageSize.toString()} onValueChange={handlePageSizeChange}>
            <SelectTrigger className="w-20 h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {pageSizes.map((size) => (
                <SelectItem key={size} value={size.toString()}>
                  {size}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Actions */}
          {actions}
        </div>
      </div>

      {/* Filters Bar */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        {/* Search */}
        {searchable && (
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={searchPlaceholder}
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10 h-8"
            />
          </div>
        )}

        {/* Custom Filters */}
        {filters}

        {/* Export Button */}
        {showExport && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-8">
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              {exportFormats.map((format) => (
                <DropdownMenuItem key={format} onClick={() => handleExport(format)}>
                  {format}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>

      {/* Desktop Table */}
      <div className="hidden md:block rounded-md border">
        <Table>
          <TableHeader>
            <TableRow className="bg-muted/50">
              {columns.map((column) => (
                <TableHead key={column.key} className={column.className}>
                  {column.label}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={columns.length} className="text-center py-8">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    <span className="ml-2 text-muted-foreground">Loading...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : paginatedData.length === 0 ? (
              <TableRow>
                <TableCell colSpan={columns.length} className="text-center py-8 text-muted-foreground">
                  No results found
                </TableCell>
              </TableRow>
            ) : (
              paginatedData.map((row, index) => {
                // Generate a unique key using row data or fallback to index
                const uniqueKey = row.id || row.transacion_id || row.outbox_id || row.unique_id || `row-${index}`;
                return (
                  <TableRow key={uniqueKey} className="hover:bg-muted/50">
                    {columns.map((column) => (
                      <TableCell key={column.key} className={column.className}>
                        {column.render ? column.render(row[column.key], row) : row[column.key]}
                      </TableCell>
                    ))}
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </div>

      {/* Mobile Cards */}
      <div className="md:hidden space-y-2">
        {loading ? (
          <Card>
            <CardContent className="text-center py-8">
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                <span className="ml-2 text-muted-foreground">Loading...</span>
              </div>
            </CardContent>
          </Card>
        ) : paginatedData.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8 text-muted-foreground">
              No results found
            </CardContent>
          </Card>
        ) : (
          paginatedData.map((row, index) => {
            // Generate a unique key using row data or fallback to index
            const uniqueKey = row.id || row.transacion_id || row.outbox_id || row.unique_id || `card-${index}`;
            return (
              <Card key={uniqueKey} className="hover:bg-muted/50 transition-colors">
                <CardContent className="p-4">
                  {mobileCardRender ? (
                    mobileCardRender(row, index)
                  ) : (
                    <div className="space-y-2">
                      {columns.map((column) => (
                        <div key={column.key} className="flex justify-between items-center">
                          <span className="text-sm font-medium text-muted-foreground">
                            {column.label}:
                          </span>
                          <span className="text-sm">
                            {column.render ? column.render(row[column.key], row) : row[column.key]}
                          </span>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })
        )}
      </div>

      {/* Pagination Info and Controls */}
      <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
        {/* Pagination Info */}
        <div className="text-sm text-muted-foreground">
          {actualTotalCount > 0 && (
            <>
              Showing {((currentPage - 1) * currentPageSize) + 1} to {Math.min(currentPage * currentPageSize, actualTotalCount)} of {actualTotalCount.toLocaleString()} results
            </>
          )}
        </div>

        {/* Pagination Controls */}
        {totalPages > 1 && (
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                  className={cn(
                    "cursor-pointer",
                    currentPage === 1 && "pointer-events-none opacity-50"
                  )}
                />
              </PaginationItem>
              
              {generatePaginationItems()}
              
              <PaginationItem>
                <PaginationNext
                  onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                  className={cn(
                    "cursor-pointer",
                    currentPage === totalPages && "pointer-events-none opacity-50"
                  )}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        )}
      </div>
    </div>
  )
}