import { useState } from "react";
import { X, Send, Users, MessageSquare, Minus } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface ComposeMessagePopupProps {
  isOpen: boolean;
  onClose: () => void;
  onSend?: (data: { from: string; to: string; message: string }) => void;
}

export default function ComposeMessagePopup({ 
  isOpen, 
  onClose, 
  onSend 
}: ComposeMessagePopupProps) {
  const [isMinimized, setIsMinimized] = useState(false);
  const [from, setFrom] = useState("");
  const [to, setTo] = useState("");
  const [message, setMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const maxCharacters = 160;
  const remainingCharacters = maxCharacters - message.length;
  const totalSMS = Math.ceil(message.length / maxCharacters) || 1;

  const handleSend = async () => {
    if (!from || !to || !message.trim()) {
      return;
    }

    setIsLoading(true);
    
    // Simulate sending
    setTimeout(() => {
      setIsLoading(false);
      onSend?.({ from, to, message });
      // Reset form
      setFrom("");
      setTo("");
      setMessage("");
      onClose();
    }, 1000);
  };

  const handleBrowse = () => {
    // Implement contact selection
    console.log("Browse contacts");
  };

  if (!isOpen) return null;

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <Card className={`w-[800px] max-w-[95vw] bg-slate-800 border-slate-700 shadow-2xl transition-all duration-300 ${
        isMinimized ? 'h-14' : 'h-auto'
      }`}>
        <CardHeader className="flex flex-row items-center justify-between p-6 pb-3">
          <CardTitle className="text-white text-xl flex items-center gap-2">
            <MessageSquare className="h-6 w-6" />
            Compose Message
          </CardTitle>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMinimized(!isMinimized)}
              className="h-8 w-8 p-0 text-gray-400 hover:text-white hover:bg-slate-700"
            >
              <Minus className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0 text-gray-400 hover:text-white hover:bg-slate-700"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>

        {!isMinimized && (
          <CardContent className="p-6 pt-0 space-y-6">
            {/* From Field */}
            <div className="space-y-2">
              <Label htmlFor="from" className="text-sm font-medium text-gray-200">
                From
              </Label>
              <Input
                id="from"
                placeholder="Sender ID"
                value={from}
                onChange={(e) => setFrom(e.target.value)}
                className="bg-slate-700 border-slate-600 text-white placeholder:text-gray-400"
              />
            </div>

            {/* To Field */}
            <div className="space-y-2">
              <Label htmlFor="to" className="text-sm font-medium text-gray-200">
                To
              </Label>
              <div className="flex gap-2">
                <Input
                  id="to"
                  placeholder="Enter Phone separated by space"
                  value={to}
                  onChange={(e) => setTo(e.target.value)}
                  className="flex-1 bg-slate-700 border-slate-600 text-white placeholder:text-gray-400"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleBrowse}
                  className="bg-slate-700 border-slate-600 text-red-400 hover:bg-slate-600 hover:text-red-300 whitespace-nowrap"
                >
                  Click to Select Contacts
                </Button>
              </div>
            </div>

            {/* Message Field */}
            <div className="space-y-2">
              <Label htmlFor="message" className="text-sm font-medium text-gray-200">
                Message
              </Label>
              <Textarea
                id="message"
                placeholder="Type your SMS here"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                className="bg-slate-700 border-slate-600 text-white placeholder:text-gray-400 min-h-[200px] resize-y"
                maxLength={maxCharacters * 5} // Allow for multiple SMS
              />
              <div className="flex items-center justify-between text-xs">
                <div className="flex items-center gap-4">
                  <span className="text-gray-400">
                    Total Character: {message.length}
                  </span>
                  <span className="text-gray-400">
                    Total SMS: {totalSMS}
                  </span>
                </div>
                <Badge 
                  variant={remainingCharacters < 0 ? "destructive" : "secondary"}
                  className="text-xs"
                >
                  {remainingCharacters >= 0 ? remainingCharacters : `+${Math.abs(remainingCharacters)}`}
                </Badge>
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center justify-between pt-2">
              <div className="text-xs text-gray-400">
                Send message via our API
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onClose}
                  className="bg-slate-700 border-slate-600 text-white hover:bg-slate-600"
                >
                  Browse
                </Button>
                <Button
                  onClick={handleSend}
                  disabled={!from || !to || !message.trim() || isLoading}
                  className="bg-red-600 hover:bg-red-700 text-white"
                  size="sm"
                >
                  {isLoading ? (
                    "Sending..."
                  ) : (
                    <>
                      <Send className="h-4 w-4 mr-2" />
                      Send
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  );
}
